<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="37a67674-3842-45e0-a45e-a656ff2f3286" name="更改" comment="refactor: 🛠️ 移除项目自动同步相关功能和状态字段">
      <change beforePath="$PROJECT_DIR$/.eslintrc-auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/.eslintrc-auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/projects/prj_projects/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/projects/prj_projects/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GradleLocalSettings">
    <option name="myGradleUserHome" value="$PROJECT_DIR$/../../../../Tool/RepositoryGradle" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Tool\apache-maven-3.9.6" />
        <option name="localRepository" value="D:\Tool\Repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Tool\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2wOs6Z6fAMLdeBVGF8OQt3YRxwn" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.Workspaces.CheckRemotesActivity&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.restore.workspace.module&quot;: &quot;true&quot;,
    &quot;com.github.patou.gitmoji.display-icon&quot;: &quot;icon&quot;,
    &quot;com.github.patou.gitmoji.include-gitmoji-description&quot;: &quot;true&quot;,
    &quot;com.github.patou.gitmoji.text-after-unicode&quot;: &quot; &quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;js.last.introduce.type&quot;: &quot;CONST&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Work/job/municipal-safety/municipal-safety-ui/src&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build:prod.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\Work\\job\\municipal-safety\\municipal-safety-ui\\node_modules\\prettier&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Work\\job\\municipal-safety\\municipal-safety-ui\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Work\job\municipal-safety\municipal-safety-ui\src" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-ui\src\hooks" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-ui\src\components" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-ui\src\views\system" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-ui\src\api\system" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="build:prod" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:prod" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.build:prod" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="37a67674-3842-45e0-a45e-a656ff2f3286" name="更改" comment="" />
      <created>1731552086285</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1731552086285</updated>
      <workItem from="1731552090134" duration="20000" />
      <workItem from="1745923781528" duration="965000" />
      <workItem from="1745925214192" duration="3183000" />
      <workItem from="1745973361694" duration="3706000" />
      <workItem from="1745984241079" duration="5704000" />
      <workItem from="1746405588680" duration="2000" />
      <workItem from="1746493290256" duration="4199000" />
      <workItem from="1746524111425" duration="4392000" />
      <workItem from="1746578413683" duration="2578000" />
      <workItem from="1746584366431" duration="22491000" />
      <workItem from="1746667466886" duration="794000" />
      <workItem from="1746668406638" duration="20371000" />
      <workItem from="1746751560405" duration="275000" />
      <workItem from="1746756201636" duration="284000" />
      <workItem from="1746757109114" duration="27579000" />
      <workItem from="1747010571287" duration="10187000" />
      <workItem from="1747096752247" duration="4601000" />
      <workItem from="1747122042186" duration="19260000" />
      <workItem from="1747204010501" duration="5295000" />
      <workItem from="1747273181692" duration="1723000" />
      <workItem from="1747292071344" duration="23683000" />
      <workItem from="1747447657209" duration="2481000" />
      <workItem from="1747615083064" duration="15038000" />
      <workItem from="1747713807753" duration="685000" />
      <workItem from="1747723123003" duration="31738000" />
      <workItem from="1747814439905" duration="5302000" />
      <workItem from="1747838345641" duration="6380000" />
      <workItem from="1747960432593" duration="598000" />
      <workItem from="1747987338495" duration="698000" />
      <workItem from="1748227814765" duration="714000" />
      <workItem from="1748517076430" duration="2206000" />
      <workItem from="1749713271926" duration="1402000" />
      <workItem from="1750154464020" duration="7000" />
      <workItem from="1750317681376" duration="9477000" />
      <workItem from="1750382477253" duration="8663000" />
      <workItem from="1750469431828" duration="11512000" />
      <workItem from="1750562432455" duration="5258000" />
      <workItem from="1750849248810" duration="3990000" />
      <workItem from="1750900819191" duration="2932000" />
      <workItem from="1750903991221" duration="713000" />
      <workItem from="1750917774120" duration="1842000" />
      <workItem from="1751965961637" duration="8741000" />
      <workItem from="1751978192107" duration="4000" />
      <workItem from="1753692120598" duration="4529000" />
      <workItem from="1753751933176" duration="3869000" />
      <workItem from="1753770444029" duration="5200000" />
      <workItem from="1753836288899" duration="1133000" />
      <workItem from="1753837688697" duration="9342000" />
      <workItem from="1753862853741" duration="7621000" />
      <workItem from="1753923639037" duration="1721000" />
    </task>
    <task id="LOCAL-00005" summary="feat(master): ✨️添加组织机构类型和代码字段到部门管理">
      <option name="closed" value="true" />
      <created>1746607529150</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1746607529150</updated>
    </task>
    <task id="LOCAL-00006" summary="feat(master): ✨️添加项目录入功能">
      <option name="closed" value="true" />
      <created>1746614171366</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746614171366</updated>
    </task>
    <task id="LOCAL-00007" summary="feat(master): ✨️添加项目日期范围选择和项目详情弹窗 v1">
      <option name="closed" value="true" />
      <created>1746614549888</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746614549888</updated>
    </task>
    <task id="LOCAL-00008" summary="feat(master): ✨️添加单位选择功能和相关对话框 v2">
      <option name="closed" value="true" />
      <created>1746617863124</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746617863124</updated>
    </task>
    <task id="LOCAL-00009" summary="feat(master): ✨️添加地区树">
      <option name="closed" value="true" />
      <created>1746671033163</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746671033163</updated>
    </task>
    <task id="LOCAL-00010" summary="feat(master): ✨️优化单位选择功能">
      <option name="closed" value="true" />
      <created>1746671728975</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746671728975</updated>
    </task>
    <task id="LOCAL-00011" summary="feat(master): ✨️添加地区选择">
      <option name="closed" value="true" />
      <created>1746694987773</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746694987773</updated>
    </task>
    <task id="LOCAL-00012" summary="feat(master): ✨️添加单位名称字段并优化地区选择功能">
      <option name="closed" value="true" />
      <created>1746705377885</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746705377885</updated>
    </task>
    <task id="LOCAL-00013" summary="feat(master): ✨️优化单位选择功能，增加单位名称字段和行样式">
      <option name="closed" value="true" />
      <created>1746758879000</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746758879000</updated>
    </task>
    <task id="LOCAL-00014" summary="feat(master): ✨️添加地区ID格式化功能">
      <option name="closed" value="true" />
      <created>1746782008594</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746782008594</updated>
    </task>
    <task id="LOCAL-00015" summary="revert(master): 🔙️回滚地址">
      <option name="closed" value="true" />
      <created>1746849209096</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746849209096</updated>
    </task>
    <task id="LOCAL-00016" summary="feat: ✨️ 添加地区选择功能，更新表单字段和处理逻辑">
      <option name="closed" value="true" />
      <created>1746945673090</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1746945673090</updated>
    </task>
    <task id="LOCAL-00017" summary="feat: ✨️ 添加项目位置链接，更新表格展示">
      <option name="closed" value="true" />
      <created>1746946867985</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1746946867985</updated>
    </task>
    <task id="LOCAL-00018" summary="feat: ✨️ 添加占地面积和预算投资总额字段，更新项目录入表单">
      <option name="closed" value="true" />
      <created>1746948126482</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1746948126482</updated>
    </task>
    <task id="LOCAL-00019" summary="feat: ✨️ 调整项目录入表单布局，优化字段展示和文件上传组件">
      <option name="closed" value="true" />
      <created>1746948630919</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1746948630919</updated>
    </task>
    <task id="LOCAL-00020" summary="feat: ✨️ 添加项目管理按钮和查看地图功能，优化项目详情展示">
      <option name="closed" value="true" />
      <created>1747122923640</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747122923640</updated>
    </task>
    <task id="LOCAL-00021" summary="feat: ✨️ 添加查询部门列表功能，移除导出按钮">
      <option name="closed" value="true" />
      <created>1747126634808</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747126634808</updated>
    </task>
    <task id="LOCAL-00022" summary="feat: ✨️ 添加用户部门信息，更新施工单位选择逻辑">
      <option name="closed" value="true" />
      <created>1747135707950</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747135707950</updated>
    </task>
    <task id="LOCAL-00023" summary="feat: ✨️ 添加监督站信息字段，优化施工单位选择逻辑">
      <option name="closed" value="true" />
      <created>1747140773140</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747140773140</updated>
    </task>
    <task id="LOCAL-00024" summary="feat: ✨️ 优化项目表单，添加所属监督站选择功能并调整布局">
      <option name="closed" value="true" />
      <created>1747182525950</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747182525950</updated>
    </task>
    <task id="LOCAL-00025" summary="feat: ✨️ 删除多余页面">
      <option name="closed" value="true" />
      <created>1747292213520</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747292213520</updated>
    </task>
    <task id="LOCAL-00026" summary="feat: ✨️ add amap-jsapi-loader dependency">
      <option name="closed" value="true" />
      <created>1747318975805</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747318975805</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: ✨️ add new route for dashboard and update API base path">
      <option name="closed" value="true" />
      <created>1747323320362</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747323320362</updated>
    </task>
    <task id="LOCAL-00028" summary="fix: 🐛 update breadcrumb names to reflect correct country name and adjust weather fetching logic">
      <option name="closed" value="true" />
      <created>1747355800406</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747355800406</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: ✨️ add new dashboard route and update weather data fetching logic">
      <option name="closed" value="true" />
      <created>1747362552968</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747362552968</updated>
    </task>
    <task id="LOCAL-00030" summary="fix: 🐛 convert provinceAdcode to string before extracting province code">
      <option name="closed" value="true" />
      <created>1747364109960</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747364109960</updated>
    </task>
    <task id="LOCAL-00031" summary="fix: 🐛 update import paths for BorderBox components in index.vue">
      <option name="closed" value="true" />
      <created>1747625159437</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1747625159437</updated>
    </task>
    <task id="LOCAL-00032" summary="fix: 🐛 update application context path and base API for production environment">
      <option name="closed" value="true" />
      <created>1747625586635</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1747625586635</updated>
    </task>
    <task id="LOCAL-00033" summary="fix: 🐛 update application context path for production environment">
      <option name="closed" value="true" />
      <created>1747626345741</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1747626345741</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(master): ✨️添加ai隐患">
      <option name="closed" value="true" />
      <created>1747729915640</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1747729915640</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(master): ✨️优化页面">
      <option name="closed" value="true" />
      <created>1747732015516</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1747732015516</updated>
    </task>
    <task id="LOCAL-00036" summary="feat: ✨️ 添加AI处理后的照片文档ID和项目、危大工程信息字段">
      <option name="closed" value="true" />
      <created>1747733879671</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1747733879671</updated>
    </task>
    <task id="LOCAL-00037" summary="fix: 🐛 update tooltip text for project management button">
      <option name="closed" value="true" />
      <created>1747739353063</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1747739353063</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: ✨️ 添加预警来源字段并优化详情展示">
      <option name="closed" value="true" />
      <created>1747739406821</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1747739406821</updated>
    </task>
    <task id="LOCAL-00039" summary="feat: ✨️ 使用ImagePreview组件替换el-image并添加AI照片文档ID">
      <option name="closed" value="true" />
      <created>1747741505446</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1747741505446</updated>
    </task>
    <task id="LOCAL-00040" summary="feat: ✨️ 添加照片文档URL和AI处理后的照片文档URL字段">
      <option name="closed" value="true" />
      <created>1747745982109</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1747745982109</updated>
    </task>
    <task id="LOCAL-00041" summary="feat(master): ✨️添加人工复检隐患字段并更新状态字典引用">
      <option name="closed" value="true" />
      <created>1747790724224</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1747790724224</updated>
    </task>
    <task id="LOCAL-00042" summary="feat: ✨️ 添加AI分析结果详情弹窗及复制功能">
      <option name="closed" value="true" />
      <created>1747791085148</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1747791085148</updated>
    </task>
    <task id="LOCAL-00043" summary="fix: 🐛 增加NODE_OPTIONS以优化生产构建内存限制">
      <option name="closed" value="true" />
      <created>1747799949300</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1747799949300</updated>
    </task>
    <task id="LOCAL-00044" summary="fix: 🐛 移除生产构建中的NODE_OPTIONS以简化构建命令">
      <option name="closed" value="true" />
      <created>1747819516070</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1747819516070</updated>
    </task>
    <task id="LOCAL-00045" summary="feat(master): ✨️添加固定数据">
      <option name="closed" value="true" />
      <created>1747841046631</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1747841046631</updated>
    </task>
    <task id="LOCAL-00046" summary="feat(master): ✨️添加政府和施工方部门及以下数据权限支持">
      <option name="closed" value="true" />
      <created>1750410316856</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750410316856</updated>
    </task>
    <task id="LOCAL-00047" summary="feat(master): ✨️添加权限控制到隐患清单标签">
      <option name="closed" value="true" />
      <created>1750496346194</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750496346194</updated>
    </task>
    <task id="LOCAL-00048" summary="feat(master): ✨️添加专家巡检权限到数据权限选项">
      <option name="closed" value="true" />
      <created>1750582268974</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750582268974</updated>
    </task>
    <task id="LOCAL-00049" summary="feat(master): ✨️添加监理数据权限">
      <option name="closed" value="true" />
      <created>1750856475750</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750856475750</updated>
    </task>
    <task id="LOCAL-00050" summary="feat: ✨️优化任务状态显示和人工复检逻辑">
      <option name="closed" value="true" />
      <created>1751971792037</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1751971792037</updated>
    </task>
    <task id="LOCAL-00051" summary="feat: ✨️优化任务状态显示和人工复检逻辑">
      <option name="closed" value="true" />
      <created>1751975042958</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1751975042958</updated>
    </task>
    <task id="LOCAL-00052" summary="feat: ✨️添加项目同步功能及相关状态管理">
      <option name="closed" value="true" />
      <created>1753848170661</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1753848170661</updated>
    </task>
    <task id="LOCAL-00053" summary="refactor: 🛠️ 移除项目自动同步相关功能和状态字段">
      <option name="closed" value="true" />
      <created>1753858389777</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753858389777</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="1b5f2c09-c609-42b7-bb75-e97ade12cd00" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="shang" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="1b5f2c09-c609-42b7-bb75-e97ade12cd00">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat(master): ✨️添加ai隐患" />
    <MESSAGE value="feat(master): ✨️优化页面" />
    <MESSAGE value="feat: ✨️ 添加AI处理后的照片文档ID和项目、危大工程信息字段" />
    <MESSAGE value="fix:  更新名称" />
    <MESSAGE value="fix:  更新名称" />
    <MESSAGE value="feat: ✨️ 添加预警来源字段并优化详情展示" />
    <MESSAGE value="feat: ✨️ 使用ImagePreview组件替换el-image并添加AI照片文档ID" />
    <MESSAGE value="feat: ✨️ 添加照片文档URL和AI处理后的照片文档URL字段" />
    <MESSAGE value="feat: ✨️ 添加人工复检隐患字段并更新状态字典引用" />
    <MESSAGE value="feat(master): ✨️添加人工复检隐患字段并更新状态字典引用" />
    <MESSAGE value="feat: ✨️ 添加AI分析结果详情弹窗及复制功能" />
    <MESSAGE value="fix:  增加NODE_OPTIONS以优化生产构建内存限制" />
    <MESSAGE value="fix:  移除生产构建中的NODE_OPTIONS以简化构建命令" />
    <MESSAGE value="style: ✨️ 添加固定数据" />
    <MESSAGE value="feat(master): ✨️添加固定数据" />
    <MESSAGE value="feat: ✨️ 添加政府和施工方的数据权限选项" />
    <MESSAGE value="feat(master): ✨️添加政府和施工方部门及以下数据权限支持" />
    <MESSAGE value="feat: ✨️" />
    <MESSAGE value="feat(master): ✨️添加权限控制到隐患清单标签" />
    <MESSAGE value="feat: ✨️ 添加专家巡检权限到数据权限选项" />
    <MESSAGE value="feat(master): ✨️添加专家巡检权限到数据权限选项" />
    <MESSAGE value="feat(master): ✨️添加监理数据权限" />
    <MESSAGE value="feat: ✨️优化任务状态显示和人工复检逻辑" />
    <MESSAGE value="feat: ✨️添加项目同步功能及相关状态管理" />
    <MESSAGE value="refactor: ️ 移除项目自动同步相关功能和状态字段" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor: ️ 移除项目自动同步相关功能和状态字段" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/views/ai/ai_haz_analysis_tasks/indexsub.vue</url>
          <line>68</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>